@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== 2025 DESIGN SYSTEM FOUNDATION ===== */

/* CSS Custom Properties System with Dynamic Theming */
:root {
  /* Core Brand Colors - 2025 Professional Palette */
  --color-navy-50: #f8fafc;
  --color-navy-100: #f1f5f9;
  --color-navy-200: #e2e8f0;
  --color-navy-300: #cbd5e1;
  --color-navy-400: #94a3b8;
  --color-navy-500: #64748b;
  --color-navy-600: #475569;
  --color-navy-700: #334155;
  --color-navy-800: #1e293b;
  --color-navy-900: #0f172a;
  --color-navy-950: #020617;

  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;
  --color-blue-950: #172554;

  --color-amber-50: #fffbeb;
  --color-amber-100: #fef3c7;
  --color-amber-200: #fde68a;
  --color-amber-300: #fcd34d;
  --color-amber-400: #fbbf24;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  --color-amber-700: #b45309;
  --color-amber-800: #92400e;
  --color-amber-900: #78350f;
  --color-amber-950: #451a03;

  /* Semantic Color System */
  --color-primary: var(--color-navy-900);
  --color-primary-hover: var(--color-navy-800);
  --color-primary-active: var(--color-navy-950);
  --color-accent: var(--color-blue-500);
  --color-accent-hover: var(--color-blue-600);
  --color-accent-active: var(--color-blue-700);
  --color-highlight: var(--color-amber-500);
  --color-highlight-hover: var(--color-amber-600);
  --color-highlight-active: var(--color-amber-700);

  /* Surface Colors */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: var(--color-navy-50);
  --color-surface-tertiary: var(--color-navy-100);
  --color-surface-elevated: #ffffff;
  --color-surface-overlay: rgba(255, 255, 255, 0.95);

  /* Text Colors */
  --color-text-primary: var(--color-navy-900);
  --color-text-secondary: var(--color-navy-700);
  --color-text-tertiary: var(--color-navy-500);
  --color-text-inverse: #ffffff;
  --color-text-accent: var(--color-blue-600);

  /* Border Colors */
  --color-border-primary: var(--color-navy-200);
  --color-border-secondary: var(--color-navy-100);
  --color-border-accent: var(--color-blue-300);
  --color-border-focus: var(--color-blue-500);

  /* Fluid Typography Scale with clamp() */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --font-size-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --font-size-6xl: clamp(3.75rem, 3rem + 3.75vw, 5rem);

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font Weights with Variable Font Support */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Adaptive Spacing System (4px base unit) */
  --space-0: 0;
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem;  /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem;    /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem;  /* 24px */
  --space-8: 2rem;    /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem;   /* 48px */
  --space-16: 4rem;   /* 64px */
  --space-20: 5rem;   /* 80px */
  --space-24: 6rem;   /* 96px */
  --space-32: 8rem;   /* 128px */

  /* Fluid Spacing with Container Queries */
  --space-fluid-xs: clamp(var(--space-2), 2vw, var(--space-4));
  --space-fluid-sm: clamp(var(--space-4), 3vw, var(--space-6));
  --space-fluid-md: clamp(var(--space-6), 4vw, var(--space-12));
  --space-fluid-lg: clamp(var(--space-12), 6vw, var(--space-20));
  --space-fluid-xl: clamp(var(--space-20), 8vw, var(--space-32));

  /* Modern Skeuomorphic Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(15, 23, 42, 0.1), 0 1px 2px -1px rgba(15, 23, 42, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -2px rgba(15, 23, 42, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.1), 0 4px 6px -4px rgba(15, 23, 42, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.1), 0 8px 10px -6px rgba(15, 23, 42, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.05);

  /* Progressive Blur Effects */
  --blur-none: 0;
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  /* Border Radius System */
  --radius-none: 0;
  --radius-sm: 0.125rem; /* 2px */
  --radius-md: 0.25rem;  /* 4px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  --radius-2xl: 1rem;    /* 16px */
  --radius-3xl: 1.5rem;  /* 24px */
  --radius-full: 9999px;

  /* Transition System */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  /* Surface Colors - Dark Mode */
  --color-surface-primary: var(--color-navy-900);
  --color-surface-secondary: var(--color-navy-800);
  --color-surface-tertiary: var(--color-navy-700);
  --color-surface-elevated: var(--color-navy-800);
  --color-surface-overlay: rgba(15, 23, 42, 0.95);

  /* Text Colors - Dark Mode */
  --color-text-primary: var(--color-navy-50);
  --color-text-secondary: var(--color-navy-200);
  --color-text-tertiary: var(--color-navy-400);
  --color-text-inverse: var(--color-navy-900);
  --color-text-accent: var(--color-blue-400);

  /* Border Colors - Dark Mode */
  --color-border-primary: var(--color-navy-700);
  --color-border-secondary: var(--color-navy-800);
  --color-border-accent: var(--color-blue-600);
  --color-border-focus: var(--color-blue-400);

  /* Adjusted Shadows for Dark Mode */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
}

/* Container Query Support */
@container (min-width: 320px) {
  .container-responsive {
    --space-container: var(--space-fluid-xs);
  }
}

@container (min-width: 640px) {
  .container-responsive {
    --space-container: var(--space-fluid-sm);
  }
}

@container (min-width: 768px) {
  .container-responsive {
    --space-container: var(--space-fluid-md);
  }
}

@container (min-width: 1024px) {
  .container-responsive {
    --space-container: var(--space-fluid-lg);
  }
}

@container (min-width: 1280px) {
  .container-responsive {
    --space-container: var(--space-fluid-xl);
  }
}
/* ===== BASE LAYER ENHANCEMENTS ===== */

@layer base {
  /* Enhanced Font Loading with Variable Font Support */
  html {
    font-family: 'Space Grotesk', system-ui, -apple-system, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: 'wght' var(--font-weight-normal);
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    background-color: var(--color-surface-primary);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    transition: background-color var(--transition-normal), color var(--transition-normal);
  }

  /* Enhanced Focus Styles */
  *:focus-visible {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
    border-radius: var(--radius-md);
  }

  /* Smooth Transitions for Theme Changes */
  * {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
}

/* ===== COMPONENT LAYER ===== */

@layer components {
  /* ===== BENTO GRID SYSTEM ===== */
  
  /* Smart Bento Grid Container */
  .bento-grid {
    display: grid;
    gap: var(--space-fluid-sm);
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-auto-rows: minmax(200px, auto);
    container-type: inline-size;
    width: 100%;
  }

  /* Advanced Bento Grid with Subgrid Support */
  .bento-grid-advanced {
    display: grid;
    gap: var(--space-fluid-sm);
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: minmax(120px, auto);
    container-type: inline-size;
    width: 100%;
  }

  /* Bento Card Base Styles */
  .bento-card {
    /* Priority-based positioning using CSS custom properties */
    --bento-priority: 3;
    --bento-weight: 0.5;
    --bento-sticky: 0;
    
    /* Dynamic sizing based on priority */
    grid-column: span calc(2 + var(--bento-priority));
    grid-row: span calc(1 + (var(--bento-weight) * 2));
    
    /* Container queries for responsive behavior */
    container-type: inline-size;
    
    /* Base visual properties */
    position: relative;
    overflow: hidden;
    
    /* Smooth transitions for layout changes */
    transition: all var(--transition-normal);
  }

  /* Priority-based sizing variants */
  .bento-card[style*="--bento-priority: 1"] {
    grid-column: span 6;
    grid-row: span 3;
  }

  .bento-card[style*="--bento-priority: 2"] {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bento-card[style*="--bento-priority: 3"] {
    grid-column: span 3;
    grid-row: span 2;
  }

  .bento-card[style*="--bento-priority: 4"] {
    grid-column: span 2;
    grid-row: span 1;
  }

  .bento-card[style*="--bento-priority: 5"] {
    grid-column: span 2;
    grid-row: span 1;
  }

  /* Sticky positioning for high-priority cards */
  .bento-card[style*="--bento-sticky: 1"] {
    position: sticky;
    top: var(--space-4);
    z-index: var(--z-sticky);
  }

  /* Container Query Responsive Breakpoints */
  @container (max-width: 640px) {
    .bento-grid-advanced {
      grid-template-columns: repeat(4, 1fr);
    }
    
    .bento-card {
      grid-column: span 4 !important;
      grid-row: span 1;
    }
  }

  @container (min-width: 641px) and (max-width: 768px) {
    .bento-grid-advanced {
      grid-template-columns: repeat(6, 1fr);
    }
    
    .bento-card[style*="--bento-priority: 1"] {
      grid-column: span 6;
    }
    
    .bento-card[style*="--bento-priority: 2"],
    .bento-card[style*="--bento-priority: 3"] {
      grid-column: span 3;
    }
    
    .bento-card[style*="--bento-priority: 4"],
    .bento-card[style*="--bento-priority: 5"] {
      grid-column: span 2;
    }
  }

  @container (min-width: 769px) and (max-width: 1024px) {
    .bento-grid-advanced {
      grid-template-columns: repeat(8, 1fr);
    }
  }

  @container (min-width: 1025px) {
    .bento-grid-advanced {
      grid-template-columns: repeat(12, 1fr);
    }
  }

  /* Gesture-aware touch interactions */
  .bento-card-touch {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Loading state for bento cards */
  .bento-card-loading {
    position: relative;
    overflow: hidden;
  }

  .bento-card-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  /* Intelligent Bento Card Enhancements */
  .intelligent-bento-card {
    /* Smooth transitions for intelligent layout changes */
    transition: all var(--transition-normal);
  }

  .intelligent-bento-card.is-visible {
    /* Enhanced visibility state */
    opacity: 1;
    transform: translateY(0);
  }

  .intelligent-bento-card:not(.has-been-visible) {
    /* Initial state for cards that haven't been seen */
    opacity: 0;
    transform: translateY(20px);
  }

  /* Priority-based visual enhancements */
  .intelligent-bento-card.priority-1 {
    /* Highest priority cards get subtle glow */
    box-shadow: var(--shadow-lg), 0 0 0 1px var(--color-accent);
  }

  .intelligent-bento-card.priority-2 {
    /* High priority cards get enhanced shadow */
    box-shadow: var(--shadow-lg);
  }

  .intelligent-bento-card.priority-5 {
    /* Lowest priority cards are more subtle */
    opacity: 0.9;
  }

  /* Accessibility enhancements */
  .intelligent-bento-card.high-contrast {
    border: 2px solid var(--color-border-primary);
    background: var(--color-surface-primary);
  }

  .intelligent-bento-card.keyboard-nav-warning {
    position: relative;
  }

  .intelligent-bento-card.keyboard-nav-warning::after {
    content: '⌨️';
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 12px;
    opacity: 0.6;
  }

  /* Smooth layout transitions */
  .bento-grid .intelligent-bento-card {
    transition: grid-column var(--transition-normal), 
                grid-row var(--transition-normal),
                transform var(--transition-normal),
                opacity var(--transition-normal);
  }

  /* Hover enhancements for intelligent cards */
  .intelligent-bento-card:hover {
    transform: translateY(-2px);
    transition-duration: var(--transition-fast);
  }

  .intelligent-bento-card.priority-1:hover {
    box-shadow: var(--shadow-xl), 0 0 0 2px var(--color-accent);
  }

  /* Focus enhancements */
  .intelligent-bento-card:focus-within {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .intelligent-bento-card,
    .intelligent-bento-card:hover,
    .bento-grid .intelligent-bento-card {
      transition: none !important;
      transform: none !important;
      animation: none !important;
    }
  }
  /* Progressive Blur Utility Classes */
  .blur-progressive-sm {
    backdrop-filter: blur(var(--blur-sm));
    -webkit-backdrop-filter: blur(var(--blur-sm));
  }

  .blur-progressive-md {
    backdrop-filter: blur(var(--blur-md));
    -webkit-backdrop-filter: blur(var(--blur-md));
  }

  .blur-progressive-lg {
    backdrop-filter: blur(var(--blur-lg));
    -webkit-backdrop-filter: blur(var(--blur-lg));
  }

  .blur-progressive-xl {
    backdrop-filter: blur(var(--blur-xl));
    -webkit-backdrop-filter: blur(var(--blur-xl));
  }

  .blur-progressive-2xl {
    backdrop-filter: blur(var(--blur-2xl));
    -webkit-backdrop-filter: blur(var(--blur-2xl));
  }

  .blur-progressive-3xl {
    backdrop-filter: blur(var(--blur-3xl));
    -webkit-backdrop-filter: blur(var(--blur-3xl));
  }

  /* Modern Skeuomorphic Card System */
  .card-elevated {
    background-color: var(--color-surface-elevated);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--color-border-secondary);
    transition: all var(--transition-normal);
  }

  .card-elevated:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
  }

  .card-interactive {
    @apply card-elevated cursor-pointer;
  }

  .card-interactive:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-4px);
  }

  .card-interactive:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  /* Glass Morphism Effects */
  .glass-light {
    background: var(--color-surface-overlay);
    backdrop-filter: blur(var(--blur-lg)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--blur-lg)) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(var(--blur-lg)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--blur-lg)) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Button System with Modern Skeuomorphism */
  .btn-primary {
    background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
    color: var(--color-text-inverse);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    border: none;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }

  .btn-secondary {
    background: var(--color-surface-elevated);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border-primary);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: var(--color-surface-secondary);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  /* Input System with Enhanced Focus States */
  .input-field {
    background: var(--color-surface-elevated);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    color: var(--color-text-primary);
    transition: all var(--transition-normal);
    width: 100%;
  }

  .input-field:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--color-surface-primary);
  }

  .input-field::placeholder {
    color: var(--color-text-tertiary);
  }

  /* Typography Utilities with Fluid Scaling */
  .text-display-1 {
    font-size: var(--font-size-6xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
  }

  .text-display-2 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
  }

  .text-heading-1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
  }

  .text-heading-2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
  }

  .text-heading-3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
  }

  .text-body-large {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
  }

  .text-body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  .text-body-small {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
  }

  .text-caption {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    color: var(--color-text-tertiary);
  }

  /* Spacing Utilities with Fluid Support */
  .space-fluid-xs { margin: var(--space-fluid-xs); }
  .space-fluid-sm { margin: var(--space-fluid-sm); }
  .space-fluid-md { margin: var(--space-fluid-md); }
  .space-fluid-lg { margin: var(--space-fluid-lg); }
  .space-fluid-xl { margin: var(--space-fluid-xl); }

  .p-fluid-xs { padding: var(--space-fluid-xs); }
  .p-fluid-sm { padding: var(--space-fluid-sm); }
  .p-fluid-md { padding: var(--space-fluid-md); }
  .p-fluid-lg { padding: var(--space-fluid-lg); }
  .p-fluid-xl { padding: var(--space-fluid-xl); }

  /* Container System with Query Support */
  .container-fluid {
    container-type: inline-size;
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-fluid-sm);
  }

  .container-responsive {
    container-type: inline-size;
    padding: var(--space-container, var(--space-fluid-sm));
  }
}

/* ===== UTILITIES LAYER ===== */

@layer utilities {
  /* Theme Toggle Utilities */
  .theme-transition {
    transition: background-color var(--transition-normal), 
                color var(--transition-normal),
                border-color var(--transition-normal);
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn var(--transition-slow) ease-out;
  }

  .animate-slide-up {
    animation: slideUp var(--transition-normal) ease-out;
  }

  .animate-scale-in {
    animation: scaleIn var(--transition-normal) var(--transition-bounce);
  }

  /* Accessibility Utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-visible-only:not(:focus-visible) {
    outline: none;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .card-elevated {
      border-width: 2px;
    }
    
    .btn-primary, .btn-secondary {
      border-width: 2px;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* ===== KEYFRAME ANIMATIONS ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}