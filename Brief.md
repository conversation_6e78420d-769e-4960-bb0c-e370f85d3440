
# Cloud OTP — Platform Brief

**Author:** <PERSON>  
**Date:** May 17, 2025  
**Email:** <EMAIL>  
**Author URL:** [skpassegna.me](https://skpassegna.me)
**Platform:** [Online Version](https://otp-cloud-gamma.vercel.app/)

> The implementrations and considerations MUST follow world class and commercial grade production ready coding and architecture!
> No unit test is needed for now, focus only on coding a working platform.
> Strictly adhere to UI-first and SOLID principles!

---

## Project Overview

Cloud OTP is a secure, cloud-based platform for managing TOTP/HOTP secrets, designed to eliminate reliance on physical devices for one-time password (OTP) management. The platform supports individual, team, and enterprise use cases, with robust admin controls, advanced sharing, and deep integration capabilities. It is designed for extensibility, security, and seamless user experience across all account types.

---

## Platform Roles & Account Types

### 1. Super Admin & Developer Admin Dashboards
- **Super Admin**: Full platform control, including user, team, sales, and marketing account management, system settings, and audit logs.
- **Developer Admin**: Manages technical integrations, API access, developer accounts, and platform resource allocation.
- **Dashboards**:
  - Real-time analytics (usage, security events, credit balances, API activity)
  - User/team management (CRUD, status, permissions)
  - Resource management (API quotas, storage, rate limits)
  - System health, logs, and alerts
  - Coupon, invitation, and credit management
  - Payment and billing overview

### 2. Sales Accounts (Platform Admin)
- Manage and track sales-driven user and team accounts
- Create and distribute coupon codes
- Assign credits to accounts
- View sales analytics and conversion metrics
- Collaborate with marketing/admins

### 3. Marketing Accounts (Platform Admin)
- Manage marketing campaigns and referral programs
- Generate and distribute invitation codes
- Track campaign performance and coupon usage
- Collaborate with sales/admins

---

## Account Management & Access

### Account Credit Balances
- Each account has a credit balance (used for API calls, premium features, etc.)
- Super Admin and Sales Admin can add credits to any account
- Users can purchase credits via integrated payment processing
- Credit usage is tracked and visible in dashboards

### Admin-Driven Account Creation & Resource Management
- Admins can create accounts (user, team, partner) directly
- Set initial quotas, credit, and resource limits
- Assign roles and permissions
- Suspend, reactivate, or delete accounts
- Audit trail for all admin actions

### Invitation-Based Account Signup
- Accounts can be created via invitation (referral, team member, partner, or admin-generated)
- Invitation codes can be single-use or multi-use, with optional expiration
- Admins can track invitation status and usage

### Coupon-Code Support
- Super Admin and Sales Admin can create, distribute, and manage coupon codes
- Coupons can provide credit, discounts, or premium access
- Coupon usage is tracked and auditable

### Payment Processing (Paddle)
- Integrated with Paddle for payment processing
- Supports credit purchases, subscription management, and invoicing
- Handles VAT/tax, refunds, and compliance
- Payment events are logged and auditable

---

## Team & User Features

### REST API Access
- All account types (team, individual) can access a REST API for automation and integration
- API keys are managed by accounts' Admins
- API endpoints for OTP CRUD, sharing, folder management, notes, team management
- API rate limits and quotas are enforced per account

### Team Accounts & Management
- Team accounts can invite/manage members (role-based access)
- Team owners/admins can assign roles (admin, member, read-only, etc.)
- Team-based access controls for OTPs, folders, and notes
- Team activity logs and audit trails

### Single Sign-On (SSO) & Social Logins
- SSO support (SAML, OIDC, or similar)
- Social login support: Facebook, GitHub, Google, Microsoft, LinkedIn (preferred)
- Users can link/unlink social accounts
- SSO and social login events are logged

### OTP Sharing & Collaboration
- OTPs can be shared with other users or teams, either temporarily or permanently
- Sharing via secure URL (with or without password protection, expiration, and access limits) w/o non platform users
- Shared OTPs can be revoked at any time
- Audit logs for all sharing actions

### OTP Grouping & Folder Management
- OTPs can be organized into folders (which can be shared)
- Folders support access controls (team, user, public link)
- Folders can be nested or tagged for organization

### Team-Based Access Management
- Fine-grained access controls for OTPs, folders, and notes
- Role-based permissions (view, edit, share, delete)
- Access can be granted/revoked by team admins or platform admins

### Notes on OTP Entries
- Users can attach notes to OTP entries (e.g., usage instructions, context)
- Notes are encrypted and access-controlled
- Notes can be shared along with OTPs or folders

---

## Support & Ticketing

### Built-In Support/Ticketing System
- Users and teams can submit support tickets directly from the platform
- Tickets are routed to Admins (Super, Sales, Developer)
- Admins can respond, escalate, and resolve tickets
- Ticket status, history, and communication are tracked
- Support analytics and satisfaction metrics available to Admins

---

## Security & Compliance
- All sensitive data (OTPs, notes, user info) is encrypted at rest and in transit using AES-256 and TLS 1.3+.
- Zero-knowledge encryption for all user-generated content (only the user can decrypt).
- Full audit logging for all critical actions (admin, user, API).
- Role-based access control (RBAC) throughout the platform.
- GDPR, and CCPA compliance support.
- Dynamic session timeouts and mandatory 2FA for all administrative access.
- Secure password management based on a zero-knowledge architecture.

### Secondary Password & Zero-Knowledge Encryption

Cloud OTP implements a robust, zero-knowledge encryption model to ensure that only the user (or authorized team members) can ever access their OTP secrets and related data. This is achieved through a user-created secondary password, which acts as the master key for all encryption and decryption operations.

#### Secondary Password (Encryption Password)
- **Purpose:** Used exclusively to encrypt and decrypt all OTP secrets and sensitive data. It is never sent to the server or stored in any form, ensuring that platform administrators cannot access user data.
- **Setup:**
  - On first use, users must create a strong secondary password, which is validated client-side to meet strict security requirements (e.g., minimum length of 8 characters, high entropy, no common patterns, based on NIST guidelines).
  - A cryptographically secure random data encryption key (DEK) and a unique salt are generated client-side.
  - The secondary password and salt are processed through a strong key derivation function (KDF) to derive a master key.
  - The DEK is then encrypted with this master key and stored in the database. The plaintext DEK never leaves the user's device.
- **Unlocking:**
  - To access encrypted data, the user must enter their secondary password.
  - The application re-derives the master key using the provided password and the stored salt, then attempts to decrypt the DEK.
  - If successful, the DEK is loaded into memory for the session. If the password is incorrect, decryption fails, and access is denied.
- **Session Management:**
  - The decrypted DEK is held in memory only for the duration of an active session (e.g., 15 minutes of inactivity, configurable by the user).
  - When the session expires or is manually locked, the DEK is securely wiped from memory.
- **Password Change:**
  - Users can change their secondary password at any time, provided they know the current one. The DEK is re-encrypted with a new master key derived from the new password.

#### Data Recovery & Account Lockout Policy
- **No Recovery:** The platform is built on the principle that user data is unrecoverable if the secondary password is lost. There is **no backup code or recovery key mechanism**. This design choice is fundamental to the zero-knowledge security model.
- **Lost Password:** If a user forgets their secondary password, their encrypted data will be permanently inaccessible. Platform administrators have no technical means to reset the password or decrypt the user's data. All appeals will be denied, as access is cryptographically impossible.
- **Account Lockout on Failed Attempts:** To protect against brute-force attacks, a strict lockout policy is enforced on the secondary password:
  - **5 incorrect attempts:** The account is locked for **24 hours**. An email notification is sent to the user.
  - **6th incorrect attempt:** The account is locked for **48 hours**.
  - **7th incorrect attempt:** The account is locked for **72 hours**.
  - **8th incorrect attempt:** The account is locked for **1 week**.
  - **9th incorrect attempt:** The account is locked for **1 month**.
  - **10th incorrect attempt:** The account is locked for **1 year**.
  - **11th incorrect attempt:** The account is **permanently locked**. The data remains encrypted and inaccessible forever.

#### Cryptographic Principles
- **All cryptographic operations** are performed client-side using strong, industry-standard algorithms.
- **Zero-Knowledge:** The secondary password is never transmitted to the server, ensuring that only the user can derive the key to their data.
- **Database Security:** The database only stores encrypted data keys, initialization vectors (IVs), and salts—never secrets or passwords in any decryptable form.
- **Password Validation:** Strict client-side validation is enforced at setup and change, including checks against common password lists and entropy estimation.

#### Further Improvements
- **Automated Encrypted Backup:** Allow users to export an encrypted backup of their OTP vault, decryptable only with their secondary password.

**Summary:**
Cloud OTP's encryption model is designed to be foolproof: only the user can ever decrypt their data. If the secondary password is lost, the data is **permanently and irreversibly unrecoverable**. No one, not even platform administrators, can recover data for any user. This strict approach guarantees the highest level of security and user privacy, placing full control and responsibility in the hands of the user.

---

## Extensibility & Integration
- REST API for all major features (OTP, folders, sharing, notes, teams, support)
- Webhooks for key events (sharing, payment, support, etc.)
- Modular design for future feature expansion (e.g., browser extension, mobile & desktop app)

---

## Usage Scenarios
- **Enterprise**: Centralized OTP management for teams, SSO, audit, and compliance
- **SMB/Startup**: Team-based OTP sharing, folder management, and support
- **Individual**: Secure, phone-free OTP management, sharing, and backup
- **Partners/Resellers**: Admin-driven account creation, credit/coupon management, and analytics

---

## Non-Goals
- Cloud OTP does not store or manage user passwords for third-party services
- Does not provide SMS/voice OTP delivery
- Does not act as a general password manager (focus is on OTP/TOTP/HOTP)

---

## Features/integrations Roadmap
- Browser extension for autofill and quick access
- Mobile & Desktop app for offline/backup OTP access

---

*This brief is exhaustive and designed to guide a full-featured, secure, and scalable implementation of Cloud OTP, agnostic of any specific technology stack.* 