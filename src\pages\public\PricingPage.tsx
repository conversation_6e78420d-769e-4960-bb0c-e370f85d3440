import React from 'react';
import { useTranslation } from 'react-i18next';
import { Check, AlertCircle, X } from 'lucide-react';
// PublicLayout is already provided in App.tsx
import CTASection from '../../components/public/CTASection';
import FeatureStatusIndicator from '../../components/public/FeatureStatusIndicator';
import FeatureMatrix from '../../components/public/FeatureMatrix';

interface PricingFeature {
  name: string;
  available: boolean;
  status: 'available' | 'development' | 'planned';
  description?: string;
}

interface PricingPlan {
  name: string;
  price: string;
  description: string;
  features: PricingFeature[];
  cta: string;
  popular?: boolean;
  comingSoon?: boolean;
}

const PricingPage: React.FC = () => {
  const { t } = useTranslation(['pricing']);

  const plans: PricingPlan[] = [
    {
      name: 'Free',
      price: '$0',
      description: 'Perfect for personal use and getting started with secure OTP management.',
      features: [
        { name: 'Unlimited OTP Storage', available: true, status: 'available' },
        { name: 'Zero-Knowledge Encryption', available: true, status: 'available' },
        { name: 'Multi-Device Sync', available: true, status: 'available' },
        { name: 'Backup & Recovery', available: true, status: 'available' },
        { name: 'Multi-Language Support', available: true, status: 'available' },
        { name: 'Basic API Access', available: true, status: 'available' },
        { name: 'Community Support', available: true, status: 'available' },
        { name: 'Team Sharing', available: false, status: 'development' },
        { name: 'Advanced Analytics', available: false, status: 'planned' },
        { name: 'Priority Support', available: false, status: 'planned' }
      ],
      cta: 'Get Started Free'
    },
    {
      name: 'Pro',
      price: '$9',
      description: 'Enhanced features for power users and small teams.',
      features: [
        { name: 'Everything in Free', available: true, status: 'available' },
        { name: 'Team Sharing (up to 5 members)', available: true, status: 'development' },
        { name: 'Advanced Backup Options', available: true, status: 'development' },
        { name: 'Usage Analytics', available: true, status: 'development' },
        { name: 'Priority Email Support', available: true, status: 'development' },
        { name: 'Custom Folders & Tags', available: true, status: 'development' },
        { name: 'Export/Import Tools', available: true, status: 'development' },
        { name: 'Advanced API Features', available: true, status: 'development' },
        { name: 'SSO Integration', available: false, status: 'planned' },
        { name: 'Audit Logs', available: false, status: 'planned' }
      ],
      cta: 'Join Waitlist',
      popular: true,
      comingSoon: true
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      description: 'Full-featured solution for organizations with advanced security needs.',
      features: [
        { name: 'Everything in Pro', available: true, status: 'development' },
        { name: 'Unlimited Team Members', available: true, status: 'development' },
        { name: 'Advanced Admin Controls', available: true, status: 'development' },
        { name: 'SSO & SAML Integration', available: true, status: 'planned' },
        { name: 'Compliance Reporting', available: true, status: 'planned' },
        { name: 'Dedicated Support', available: true, status: 'planned' },
        { name: 'Custom Integrations', available: true, status: 'planned' },
        { name: 'On-Premise Deployment', available: true, status: 'planned' },
        { name: 'SLA Guarantees', available: true, status: 'planned' },
        { name: 'White-Label Options', available: true, status: 'planned' }
      ],
      cta: 'Contact Sales',
      comingSoon: true
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-700 to-primary-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">{t('pricing:hero.title')}</h1>
          <p className="text-xl max-w-3xl mx-auto mb-8">{t('pricing:hero.subtitle')}</p>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-dark-900">{t('pricing:choosePlan')}</h2>
            <p className="mt-4 text-lg text-dark-600 max-w-3xl mx-auto">{t('pricing:planDescription')}</p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`
                  bg-white rounded-lg shadow-lg overflow-hidden border transition-all
                  ${plan.popular ? 'border-primary-500 transform scale-105 md:-mt-4 md:mb-4' : 'border-gray-200'}
                `}
              >
                {plan.popular && (
                  <div className="bg-primary-500 text-white text-center py-2 px-4 text-sm font-medium">
                    {t('pricing:popularChoice')}
                  </div>
                )}

                <div className="p-8">
                  <h3 className="text-2xl font-bold text-dark-900">{plan.name}</h3>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-extrabold text-dark-900">{plan.price}</span>
                    {plan.price !== t('pricing:plans.enterprise.price') && (
                      <span className="ml-1 text-xl text-dark-600">/month</span>
                    )}
                  </div>
                  <p className="mt-5 text-lg text-dark-600">{plan.description}</p>

                  {plan.comingSoon && (
                    <div className="mt-6 inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      {t('pricing:comingSoon')}
                    </div>
                  )}

                  <ul className="mt-6 space-y-4">
                    {plan.features.map((feature, featureIdx) => (
                      <li key={featureIdx} className="flex items-start">
                        <div className="flex-shrink-0 mt-1">
                          {feature.available ? (
                            <Check className="h-5 w-5 text-green-500" />
                          ) : (
                            <X className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex items-center gap-2">
                            <span className={`text-sm ${feature.available ? 'text-dark-700' : 'text-dark-500'}`}>
                              {feature.name}
                            </span>
                            {feature.status !== 'available' && (
                              <FeatureStatusIndicator 
                                status={feature.status} 
                                showText={false}
                                className="text-xs px-2 py-0.5"
                              />
                            )}
                          </div>
                          {feature.description && (
                            <p className="text-xs text-dark-500 mt-1">{feature.description}</p>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>

                  <div className="mt-8">
                    <a
                      href={plan.comingSoon ? '#waitlist' : '/auth'}
                      className={`
                        block w-full text-center rounded-md py-3 font-medium transition duration-150
                        ${plan.popular
                          ? 'bg-primary-600 text-white hover:bg-primary-700'
                          : 'bg-gray-100 text-dark-800 hover:bg-gray-200'
                        }
                      `}
                    >
                      {plan.comingSoon ? t('pricing:joinWaitlist') : plan.cta}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-dark-900">{t('pricing:faqTitle')}</h2>
            <p className="mt-4 text-lg text-dark-600 max-w-3xl mx-auto">{t('pricing:faqSubtitle')}</p>
          </div>

          <div className="max-w-3xl mx-auto divide-y divide-gray-200">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="py-6">
                <h3 className="text-lg font-medium text-dark-900">
                  {t(`pricing:faq.q${item}`)}
                </h3>
                <p className="mt-3 text-dark-600">
                  {t(`pricing:faq.a${item}`)}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Waitlist Section */}
      <section id="waitlist" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-dark-900 mb-6">{t('pricing:waitlist.title')}</h2>
            <p className="text-lg text-dark-600 mb-8">{t('pricing:waitlist.subtitle')}</p>

            <form className="mt-8 sm:flex justify-center">
              <div className="min-w-0 flex-1">
                <label htmlFor="email" className="sr-only">{t('pricing:waitlist.emailLabel')}</label>
                <input
                  id="email"
                  type="email"
                  placeholder={t('pricing:waitlist.emailPlaceholder')}
                  className="block w-full px-4 py-3 rounded-md border border-gray-300 shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div className="mt-3 sm:mt-0 sm:ml-3">
                <button
                  type="submit"
                  className="block w-full sm:w-auto rounded-md px-4 py-3 bg-primary-600 text-white font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  {t('pricing:waitlist.button')}
                </button>
              </div>
            </form>

            <p className="mt-3 text-sm text-dark-500">{t('pricing:waitlist.privacy')}</p>
          </div>
        </div>
      </section>

      {/* Feature Matrix */}
      <FeatureMatrix />

      {/* Compare Enterprise Features */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">Enterprise Solutions</h2>
            <p className="mt-4 text-lg text-gray-300 max-w-3xl mx-auto">
              Advanced features for organizations with complex security requirements
            </p>
          </div>

          <div className="mt-12 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-600 mb-4">
                <span className="text-xl font-bold text-white">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Advanced Security
              </h3>
              <p className="text-gray-300">
                SSO integration, compliance reporting, and advanced audit capabilities
              </p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-600 mb-4">
                <span className="text-xl font-bold text-white">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Dedicated Support
              </h3>
              <p className="text-gray-300">
                Priority support with dedicated account management and SLA guarantees
              </p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-600 mb-4">
                <span className="text-xl font-bold text-white">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Custom Integration
              </h3>
              <p className="text-gray-300">
                Custom API development and integration support for your existing systems
              </p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-600 mb-4">
                <span className="text-xl font-bold text-white">4</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Deployment Options
              </h3>
              <p className="text-gray-300">
                On-premise deployment and white-label solutions available
              </p>
            </div>
          </div>

          <div className="mt-12 text-center">
            <a
              href="/contact"
              className="inline-block bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition duration-150"
            >
              Contact Sales Team
            </a>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <CTASection />
    </>
  );
};

export default PricingPage;