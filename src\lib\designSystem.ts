/**
 * 2025 Design System Utilities
 * Provides TypeScript utilities for working with the design system
 */

// Theme Management
export type Theme = 'light' | 'dark';

export const themeManager = {
  /**
   * Set the theme for the application
   */
  setTheme: (theme: Theme) => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
  },

  /**
   * Get the current theme
   */
  getTheme: (): Theme => {
    const stored = localStorage.getItem('theme') as Theme;
    if (stored) return stored;
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  },

  /**
   * Toggle between light and dark themes
   */
  toggleTheme: () => {
    const current = themeManager.getTheme();
    const next = current === 'light' ? 'dark' : 'light';
    themeManager.setTheme(next);
    return next;
  },

  /**
   * Initialize theme on app startup
   */
  initialize: () => {
    const theme = themeManager.getTheme();
    themeManager.setTheme(theme);
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        themeManager.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  },
};

// Design System Constants
export const designTokens = {
  // Spacing Scale
  spacing: {
    0: '0',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    32: '8rem',
  },
  
  // Fluid Spacing
  fluidSpacing: {
    xs: 'var(--space-fluid-xs)',
    sm: 'var(--space-fluid-sm)',
    md: 'var(--space-fluid-md)',
    lg: 'var(--space-fluid-lg)',
    xl: 'var(--space-fluid-xl)',
  },

  // Typography Scale
  fontSize: {
    xs: 'var(--font-size-xs)',
    sm: 'var(--font-size-sm)',
    base: 'var(--font-size-base)',
    lg: 'var(--font-size-lg)',
    xl: 'var(--font-size-xl)',
    '2xl': 'var(--font-size-2xl)',
    '3xl': 'var(--font-size-3xl)',
    '4xl': 'var(--font-size-4xl)',
    '5xl': 'var(--font-size-5xl)',
    '6xl': 'var(--font-size-6xl)',
  },

  // Shadow System
  shadows: {
    xs: 'var(--shadow-xs)',
    sm: 'var(--shadow-sm)',
    md: 'var(--shadow-md)',
    lg: 'var(--shadow-lg)',
    xl: 'var(--shadow-xl)',
    '2xl': 'var(--shadow-2xl)',
    inner: 'var(--shadow-inner)',
  },

  // Border Radius
  borderRadius: {
    none: '0',
    sm: 'var(--radius-sm)',
    md: 'var(--radius-md)',
    lg: 'var(--radius-lg)',
    xl: 'var(--radius-xl)',
    '2xl': 'var(--radius-2xl)',
    '3xl': 'var(--radius-3xl)',
    full: '9999px',
  },

  // Blur Effects
  blur: {
    none: '0',
    sm: 'var(--blur-sm)',
    md: 'var(--blur-md)',
    lg: 'var(--blur-lg)',
    xl: 'var(--blur-xl)',
    '2xl': 'var(--blur-2xl)',
    '3xl': 'var(--blur-3xl)',
  },

  // Transitions
  transitions: {
    fast: 'var(--transition-fast)',
    normal: 'var(--transition-normal)',
    slow: 'var(--transition-slow)',
    bounce: 'var(--transition-bounce)',
  },

  // Z-Index Scale
  zIndex: {
    dropdown: 'var(--z-dropdown)',
    sticky: 'var(--z-sticky)',
    fixed: 'var(--z-fixed)',
    modalBackdrop: 'var(--z-modal-backdrop)',
    modal: 'var(--z-modal)',
    popover: 'var(--z-popover)',
    tooltip: 'var(--z-tooltip)',
    toast: 'var(--z-toast)',
  },
} as const;

// Component Variants
export const componentVariants = {
  button: {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
  },
  card: {
    elevated: 'card-elevated',
    interactive: 'card-interactive',
  },
  glass: {
    light: 'glass-light',
    dark: 'glass-dark',
  },
  blur: {
    sm: 'blur-progressive-sm',
    md: 'blur-progressive-md',
    lg: 'blur-progressive-lg',
    xl: 'blur-progressive-xl',
    '2xl': 'blur-progressive-2xl',
    '3xl': 'blur-progressive-3xl',
  },
  text: {
    display1: 'text-display-1',
    display2: 'text-display-2',
    heading1: 'text-heading-1',
    heading2: 'text-heading-2',
    heading3: 'text-heading-3',
    bodyLarge: 'text-body-large',
    body: 'text-body',
    bodySmall: 'text-body-small',
    caption: 'text-caption',
  },
} as const;

// Utility Functions
export const designUtils = {
  /**
   * Combine CSS classes with proper handling of conditionals
   */
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(' ');
  },

  /**
   * Get CSS custom property value
   */
  getCSSVar: (property: string): string => {
    return getComputedStyle(document.documentElement).getPropertyValue(property);
  },

  /**
   * Set CSS custom property value
   */
  setCSSVar: (property: string, value: string): void => {
    document.documentElement.style.setProperty(property, value);
  },

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Check if user prefers high contrast
   */
  prefersHighContrast: (): boolean => {
    return window.matchMedia('(prefers-contrast: high)').matches;
  },

  /**
   * Get responsive breakpoint information
   */
  getBreakpoint: (): 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' => {
    const width = window.innerWidth;
    if (width < 640) return 'xs';
    if (width < 768) return 'sm';
    if (width < 1024) return 'md';
    if (width < 1280) return 'lg';
    if (width < 1536) return 'xl';
    return '2xl';
  },

  /**
   * Create a container query observer
   */
  createContainerObserver: (
    element: HTMLElement,
    callback: (size: { width: number; height: number }) => void
  ): ResizeObserver => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        callback({ width, height });
      }
    });
    
    observer.observe(element);
    return observer;
  },
};

// Type Definitions for Design System
export type SpacingToken = keyof typeof designTokens.spacing;
export type FluidSpacingToken = keyof typeof designTokens.fluidSpacing;
export type FontSizeToken = keyof typeof designTokens.fontSize;
export type ShadowToken = keyof typeof designTokens.shadows;
export type BorderRadiusToken = keyof typeof designTokens.borderRadius;
export type BlurToken = keyof typeof designTokens.blur;
export type TransitionToken = keyof typeof designTokens.transitions;
export type ZIndexToken = keyof typeof designTokens.zIndex;

export type ButtonVariant = keyof typeof componentVariants.button;
export type CardVariant = keyof typeof componentVariants.card;
export type GlassVariant = keyof typeof componentVariants.glass;
export type BlurVariant = keyof typeof componentVariants.blur;
export type TextVariant = keyof typeof componentVariants.text;

// Export everything for easy access
export default {
  themeManager,
  designTokens,
  componentVariants,
  designUtils,
};