import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Globe, Database } from 'lucide-react';
import ImmersiveHeroSection from '../../components/public/ImmersiveHeroSection';
import CleanFeaturesSection from '../../components/public/CleanFeaturesSection';
import SecurityCertificationsSection from '../../components/public/SecurityCertificationsSection';
import CTASection from '../../components/public/CTASection';
import ComingSoonCard from '../../components/public/ComingSoonCard';

const HomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'public']);

  return (
    <>
      {/* Immersive Hero Section with 2025 Design Trends */}
      <ImmersiveHeroSection />

      {/* Clean, Responsive Features Section */}
      <CleanFeaturesSection />

      {/* Coming Soon Products */}
      <section className="py-16 bg-dark-800 border-y border-dark-700 relative overflow-hidden">
        {/* Éléments décoratifs pixelisés */}
        <div className="absolute top-10 right-10 w-32 h-32 opacity-10 z-0">
          <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px] animate-pulse-slow"></div>
        </div>
        <div className="absolute bottom-10 left-10 w-40 h-40 opacity-10 z-0">
          <div className="absolute w-full h-full bg-pixel-pattern bg-[size:15px_15px] animate-pulse-slow"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white sm:text-4xl font-mono">
              {t('public:comingSoon')}
            </h2>
            <p className="mt-4 text-xl text-primary-300 max-w-3xl mx-auto">
              {t('public:comingSoonSubtitle')}
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <ComingSoonCard
              title={t('public:browserExtensionTitle')}
              description={t('public:browserExtensionDescription')}
              icon={Globe}
              date="Q1 2025"
            />

            <ComingSoonCard
              title={t('public:mobileAppTitle')}
              description={t('public:mobileAppDescription')}
              icon={Shield}
              date="Q2 2025"
            />

            <ComingSoonCard
              title={t('public:desktopAppTitle')}
              description={t('public:desktopAppDescription')}
              icon={Database}
              date="Q3 2025"
            />
          </div>
        </div>
      </section>

      {/* Security Certifications Section */}
      <SecurityCertificationsSection />

      {/* Call to Action */}
      <CTASection />
    </>
  );
};

export default HomePage;